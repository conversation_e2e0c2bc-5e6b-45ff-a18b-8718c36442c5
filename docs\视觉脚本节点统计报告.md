# 视觉脚本系统节点统计报告

生成时间: 2025/6/27 12:21:05

## 总体统计

- **总节点数量**: 150
- **节点文件数量**: 37
- **节点类别数量**: 13

## 按类别统计

- **其他节点**: 85 个节点
- **核心节点**: 11 个节点
- **数学节点**: 11 个节点
- **AI节点**: 7 个节点
- **调试节点**: 7 个节点
- **物理节点**: 6 个节点
- **实体节点**: 5 个节点
- **音频节点**: 4 个节点
- **输入节点**: 4 个节点
- **网络节点**: 4 个节点
- **UI节点**: 3 个节点
- **动画节点**: 2 个节点
- **软体物理节点**: 1 个节点

## 按文件详细统计

### SpatialNodes (其他节点)

- **节点类数量**: 15
- **注册类型数量**: 0
- **节点类列表**:
  - CreateGeographicCoordinateNode
  - CoordinateTransformNode
  - CreateGeospatialComponentNode
  - AddGeospatialComponentNode
  - GetGeographicCoordinateNode
  - SetGeographicCoordinateNode
  - CalculateDistanceNode
  - BufferAnalysisNode
  - IntersectionAnalysisNode
  - PointInPolygonNode
  - CreateGeoJSONNode
  - CreateFromGeoJSONNode
  - SetMapViewNode
  - GetMapViewNode
  - SetMapProviderNode

### CoreNodes (核心节点)

- **节点类数量**: 11
- **注册类型数量**: 0
- **节点类列表**:
  - OnStartNode
  - BranchNode
  - SequenceNode
  - ForLoopNode
  - WhileLoopNode
  - DelayNode
  - SetVariableNode
  - GetVariableNode
  - ArrayOperationNode
  - TryCatchNode
  - TypeConvertNode

### MathNodes (数学节点)

- **节点类数量**: 11
- **注册类型数量**: 0
- **节点类列表**:
  - AddNode
  - SubtractNode
  - MultiplyNode
  - DivideNode
  - PowerNode
  - SqrtNode
  - TrigonometricNode
  - VectorMathNode
  - RandomNode
  - InterpolationNode
  - MathConstantNode

### TransformNodes (其他节点)

- **节点类数量**: 8
- **注册类型数量**: 0
- **节点类列表**:
  - GetPositionNode
  - SetPositionNode
  - MoveNode
  - GetRotationNode
  - SetRotationNode
  - RotateNode
  - GetScaleNode
  - SetScaleNode

### AINodes (AI节点)

- **节点类数量**: 7
- **注册类型数量**: 0
- **节点类列表**:
  - LoadAIModelNode
  - AIInferenceNode
  - TextClassificationNode
  - EmotionAnalysisNode
  - SpeechRecognitionNode
  - SpeechSynthesisNode
  - DialogueManagementNode

### DebugNodes (调试节点)

- **节点类数量**: 7
- **注册类型数量**: 0
- **节点类列表**:
  - LogNode
  - BreakpointNode
  - PerformanceProfilerNode
  - MemoryMonitorNode
  - VariableWatcherNode
  - StackTraceNode
  - ExecutionTimerNode

### ComponentNodes (其他节点)

- **节点类数量**: 6
- **注册类型数量**: 0
- **节点类列表**:
  - AddComponentNode
  - RemoveComponentNode
  - GetComponentNode
  - ComponentEnabledNode
  - GetAllComponentsNode
  - ComponentPropertyNode

### PhysicsNodes (物理节点)

- **节点类数量**: 6
- **注册类型数量**: 0
- **节点类列表**:
  - AddRigidBodyNode
  - AddColliderNode
  - ApplyForceNode
  - SetVelocityNode
  - CollisionDetectionNode
  - RaycastNode

### EntityNodes (实体节点)

- **节点类数量**: 5
- **注册类型数量**: 0
- **节点类列表**:
  - CreateEntityNode
  - FindEntityNode
  - DestroyEntityNode
  - CloneEntityNode
  - EntityActiveNode

### IndustrialAutomationNodes (其他节点)

- **节点类数量**: 5
- **注册类型数量**: 0
- **节点类列表**:
  - DeviceManagerNode
  - DataCollectionNode
  - QualityInspectionNode
  - AlarmSystemNode
  - ProcessControlNode

### AdvancedAnimationNodes (其他节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - AnimationStateMachineNode
  - AnimationBlendNode
  - IKSystemNode
  - AnimationEventNode

### AudioNodes (音频节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - LoadAudioNode
  - PlayAudioNode
  - SpatialAudioNode
  - AudioListenerNode

### InputNodes (输入节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - KeyboardInputNode
  - MouseInputNode
  - TouchInputNode
  - GamepadInputNode

### NetworkNodes (网络节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - WebSocketNode
  - WebRTCNode
  - HTTPRequestNode
  - NetworkSyncNode

### RAGApplicationNodes (其他节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - KnowledgeBaseNode
  - RAGQueryNode
  - DocumentProcessingNode
  - SemanticSearchNode

### RenderingNodes (其他节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - MaterialSystemNode
  - LightControlNode
  - CameraManagerNode
  - RenderConfigNode

### RenderingOptimizationNodes (其他节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - LODSystemNode
  - BatchRenderingNode
  - InstancedRenderingNode
  - FrustumCullingNode

### SpatialInformationNodes (其他节点)

- **节点类数量**: 4
- **注册类型数量**: 0
- **节点类列表**:
  - GISAnalysisNode
  - SpatialQueryNode
  - GeospatialVisualizationNode
  - LocationServicesNode

### ComputerVisionNodes (其他节点)

- **节点类数量**: 3
- **注册类型数量**: 0
- **节点类列表**:
  - ObjectDetectionNode
  - ImageClassificationNode
  - FeatureExtractionNode

### AdvancedAudioNodes (其他节点)

- **节点类数量**: 3
- **注册类型数量**: 0
- **节点类列表**:
  - SpatialAudioNode
  - AudioFilterNode
  - AudioEffectNode

### BlockchainNodes (其他节点)

- **节点类数量**: 3
- **注册类型数量**: 0
- **节点类列表**:
  - WalletConnectNode
  - SmartContractNode
  - NFTOperationNode

### VRInputNodes (其他节点)

- **节点类数量**: 3
- **注册类型数量**: 0
- **节点类列表**:
  - VRControllerNode
  - GestureRecognitionNode
  - VoiceRecognitionNode

### LearningRecordNodes (其他节点)

- **节点类数量**: 3
- **注册类型数量**: 0
- **节点类列表**:
  - LearningRecordNode
  - LearningStatisticsNode
  - AchievementSystemNode

### UINodes (UI节点)

- **节点类数量**: 3
- **注册类型数量**: 0
- **节点类列表**:
  - CreateUIElementNode
  - UILayoutNode
  - UIEventHandlerNode

### AnimationNodes (动画节点)

- **节点类数量**: 2
- **注册类型数量**: 0
- **节点类列表**:
  - TweenNode
  - KeyframeAnimationNode

### ParticleSystemNodes (其他节点)

- **节点类数量**: 2
- **注册类型数量**: 0
- **节点类列表**:
  - ParticleEmitterNode
  - ParticleEffectNode

### PostProcessingNodes (其他节点)

- **节点类数量**: 2
- **注册类型数量**: 0
- **节点类列表**:
  - PostProcessEffectNode
  - ToneMappingNode

### SceneGenerationNodes (其他节点)

- **节点类数量**: 2
- **注册类型数量**: 0
- **节点类列表**:
  - AutoSceneGenerationNode
  - SceneLayoutNode

### TerrainSystemNodes (其他节点)

- **节点类数量**: 2
- **注册类型数量**: 0
- **节点类列表**:
  - TerrainGenerationNode
  - TerrainErosionNode

### WaterSystemNodes (其他节点)

- **节点类数量**: 2
- **注册类型数量**: 0
- **节点类列表**:
  - CreateWaterBodyNode
  - WaterWaveNode

### CameraInputNode (其他节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - CameraInputNode

### EyeTrackingNode (其他节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - EyeTrackingNode

### FaceDetectionNode (其他节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - FaceDetectionNode

### HandTrackingNode (其他节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - HandTrackingNode

### PoseDetectionNode (其他节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - PoseDetectionNode

### VirtualInteractionNode (其他节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - VirtualInteractionNode

### SoftBodyNodes (软体物理节点)

- **节点类数量**: 1
- **注册类型数量**: 0
- **节点类列表**:
  - ClothSystemNode

## 所有节点列表

- **AchievementSystemNode** (其他节点 - LearningRecordNodes)
- **AddColliderNode** (物理节点 - PhysicsNodes)
- **AddComponentNode** (其他节点 - ComponentNodes)
- **AddGeospatialComponentNode** (其他节点 - SpatialNodes)
- **AddNode** (数学节点 - MathNodes)
- **AddRigidBodyNode** (物理节点 - PhysicsNodes)
- **AIInferenceNode** (AI节点 - AINodes)
- **AlarmSystemNode** (其他节点 - IndustrialAutomationNodes)
- **AnimationBlendNode** (其他节点 - AdvancedAnimationNodes)
- **AnimationEventNode** (其他节点 - AdvancedAnimationNodes)
- **AnimationStateMachineNode** (其他节点 - AdvancedAnimationNodes)
- **ApplyForceNode** (物理节点 - PhysicsNodes)
- **ArrayOperationNode** (核心节点 - CoreNodes)
- **AudioEffectNode** (其他节点 - AdvancedAudioNodes)
- **AudioFilterNode** (其他节点 - AdvancedAudioNodes)
- **AudioListenerNode** (音频节点 - AudioNodes)
- **AutoSceneGenerationNode** (其他节点 - SceneGenerationNodes)
- **BatchRenderingNode** (其他节点 - RenderingOptimizationNodes)
- **BranchNode** (核心节点 - CoreNodes)
- **BreakpointNode** (调试节点 - DebugNodes)
- **BufferAnalysisNode** (其他节点 - SpatialNodes)
- **CalculateDistanceNode** (其他节点 - SpatialNodes)
- **CameraInputNode** (其他节点 - CameraInputNode)
- **CameraManagerNode** (其他节点 - RenderingNodes)
- **CloneEntityNode** (实体节点 - EntityNodes)
- **ClothSystemNode** (软体物理节点 - SoftBodyNodes)
- **CollisionDetectionNode** (物理节点 - PhysicsNodes)
- **ComponentEnabledNode** (其他节点 - ComponentNodes)
- **ComponentPropertyNode** (其他节点 - ComponentNodes)
- **CoordinateTransformNode** (其他节点 - SpatialNodes)
- **CreateEntityNode** (实体节点 - EntityNodes)
- **CreateFromGeoJSONNode** (其他节点 - SpatialNodes)
- **CreateGeographicCoordinateNode** (其他节点 - SpatialNodes)
- **CreateGeoJSONNode** (其他节点 - SpatialNodes)
- **CreateGeospatialComponentNode** (其他节点 - SpatialNodes)
- **CreateUIElementNode** (UI节点 - UINodes)
- **CreateWaterBodyNode** (其他节点 - WaterSystemNodes)
- **DataCollectionNode** (其他节点 - IndustrialAutomationNodes)
- **DelayNode** (核心节点 - CoreNodes)
- **DestroyEntityNode** (实体节点 - EntityNodes)
- **DeviceManagerNode** (其他节点 - IndustrialAutomationNodes)
- **DialogueManagementNode** (AI节点 - AINodes)
- **DivideNode** (数学节点 - MathNodes)
- **DocumentProcessingNode** (其他节点 - RAGApplicationNodes)
- **EmotionAnalysisNode** (AI节点 - AINodes)
- **EntityActiveNode** (实体节点 - EntityNodes)
- **ExecutionTimerNode** (调试节点 - DebugNodes)
- **EyeTrackingNode** (其他节点 - EyeTrackingNode)
- **FaceDetectionNode** (其他节点 - FaceDetectionNode)
- **FeatureExtractionNode** (其他节点 - ComputerVisionNodes)
- **FindEntityNode** (实体节点 - EntityNodes)
- **ForLoopNode** (核心节点 - CoreNodes)
- **FrustumCullingNode** (其他节点 - RenderingOptimizationNodes)
- **GamepadInputNode** (输入节点 - InputNodes)
- **GeospatialVisualizationNode** (其他节点 - SpatialInformationNodes)
- **GestureRecognitionNode** (其他节点 - VRInputNodes)
- **GetAllComponentsNode** (其他节点 - ComponentNodes)
- **GetComponentNode** (其他节点 - ComponentNodes)
- **GetGeographicCoordinateNode** (其他节点 - SpatialNodes)
- **GetMapViewNode** (其他节点 - SpatialNodes)
- **GetPositionNode** (其他节点 - TransformNodes)
- **GetRotationNode** (其他节点 - TransformNodes)
- **GetScaleNode** (其他节点 - TransformNodes)
- **GetVariableNode** (核心节点 - CoreNodes)
- **GISAnalysisNode** (其他节点 - SpatialInformationNodes)
- **HandTrackingNode** (其他节点 - HandTrackingNode)
- **HTTPRequestNode** (网络节点 - NetworkNodes)
- **IKSystemNode** (其他节点 - AdvancedAnimationNodes)
- **ImageClassificationNode** (其他节点 - ComputerVisionNodes)
- **InstancedRenderingNode** (其他节点 - RenderingOptimizationNodes)
- **InterpolationNode** (数学节点 - MathNodes)
- **IntersectionAnalysisNode** (其他节点 - SpatialNodes)
- **KeyboardInputNode** (输入节点 - InputNodes)
- **KeyframeAnimationNode** (动画节点 - AnimationNodes)
- **KnowledgeBaseNode** (其他节点 - RAGApplicationNodes)
- **LearningRecordNode** (其他节点 - LearningRecordNodes)
- **LearningStatisticsNode** (其他节点 - LearningRecordNodes)
- **LightControlNode** (其他节点 - RenderingNodes)
- **LoadAIModelNode** (AI节点 - AINodes)
- **LoadAudioNode** (音频节点 - AudioNodes)
- **LocationServicesNode** (其他节点 - SpatialInformationNodes)
- **LODSystemNode** (其他节点 - RenderingOptimizationNodes)
- **LogNode** (调试节点 - DebugNodes)
- **MaterialSystemNode** (其他节点 - RenderingNodes)
- **MathConstantNode** (数学节点 - MathNodes)
- **MemoryMonitorNode** (调试节点 - DebugNodes)
- **MouseInputNode** (输入节点 - InputNodes)
- **MoveNode** (其他节点 - TransformNodes)
- **MultiplyNode** (数学节点 - MathNodes)
- **NetworkSyncNode** (网络节点 - NetworkNodes)
- **NFTOperationNode** (其他节点 - BlockchainNodes)
- **ObjectDetectionNode** (其他节点 - ComputerVisionNodes)
- **OnStartNode** (核心节点 - CoreNodes)
- **ParticleEffectNode** (其他节点 - ParticleSystemNodes)
- **ParticleEmitterNode** (其他节点 - ParticleSystemNodes)
- **PerformanceProfilerNode** (调试节点 - DebugNodes)
- **PlayAudioNode** (音频节点 - AudioNodes)
- **PointInPolygonNode** (其他节点 - SpatialNodes)
- **PoseDetectionNode** (其他节点 - PoseDetectionNode)
- **PostProcessEffectNode** (其他节点 - PostProcessingNodes)
- **PowerNode** (数学节点 - MathNodes)
- **ProcessControlNode** (其他节点 - IndustrialAutomationNodes)
- **QualityInspectionNode** (其他节点 - IndustrialAutomationNodes)
- **RAGQueryNode** (其他节点 - RAGApplicationNodes)
- **RandomNode** (数学节点 - MathNodes)
- **RaycastNode** (物理节点 - PhysicsNodes)
- **RemoveComponentNode** (其他节点 - ComponentNodes)
- **RenderConfigNode** (其他节点 - RenderingNodes)
- **RotateNode** (其他节点 - TransformNodes)
- **SceneLayoutNode** (其他节点 - SceneGenerationNodes)
- **SemanticSearchNode** (其他节点 - RAGApplicationNodes)
- **SequenceNode** (核心节点 - CoreNodes)
- **SetGeographicCoordinateNode** (其他节点 - SpatialNodes)
- **SetMapProviderNode** (其他节点 - SpatialNodes)
- **SetMapViewNode** (其他节点 - SpatialNodes)
- **SetPositionNode** (其他节点 - TransformNodes)
- **SetRotationNode** (其他节点 - TransformNodes)
- **SetScaleNode** (其他节点 - TransformNodes)
- **SetVariableNode** (核心节点 - CoreNodes)
- **SetVelocityNode** (物理节点 - PhysicsNodes)
- **SmartContractNode** (其他节点 - BlockchainNodes)
- **SpatialAudioNode** (其他节点 - AdvancedAudioNodes)
- **SpatialAudioNode** (音频节点 - AudioNodes)
- **SpatialQueryNode** (其他节点 - SpatialInformationNodes)
- **SpeechRecognitionNode** (AI节点 - AINodes)
- **SpeechSynthesisNode** (AI节点 - AINodes)
- **SqrtNode** (数学节点 - MathNodes)
- **StackTraceNode** (调试节点 - DebugNodes)
- **SubtractNode** (数学节点 - MathNodes)
- **TerrainErosionNode** (其他节点 - TerrainSystemNodes)
- **TerrainGenerationNode** (其他节点 - TerrainSystemNodes)
- **TextClassificationNode** (AI节点 - AINodes)
- **ToneMappingNode** (其他节点 - PostProcessingNodes)
- **TouchInputNode** (输入节点 - InputNodes)
- **TrigonometricNode** (数学节点 - MathNodes)
- **TryCatchNode** (核心节点 - CoreNodes)
- **TweenNode** (动画节点 - AnimationNodes)
- **TypeConvertNode** (核心节点 - CoreNodes)
- **UIEventHandlerNode** (UI节点 - UINodes)
- **UILayoutNode** (UI节点 - UINodes)
- **VariableWatcherNode** (调试节点 - DebugNodes)
- **VectorMathNode** (数学节点 - MathNodes)
- **VirtualInteractionNode** (其他节点 - VirtualInteractionNode)
- **VoiceRecognitionNode** (其他节点 - VRInputNodes)
- **VRControllerNode** (其他节点 - VRInputNodes)
- **WalletConnectNode** (其他节点 - BlockchainNodes)
- **WaterWaveNode** (其他节点 - WaterSystemNodes)
- **WebRTCNode** (网络节点 - NetworkNodes)
- **WebSocketNode** (网络节点 - NetworkNodes)
- **WhileLoopNode** (核心节点 - CoreNodes)
